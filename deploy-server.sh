#!/bin/bash

# =============================================================================
# StageMinder 本地一键部署脚本
# 自动完成所有本地部署工作，包括配置修复、构建和启动
# =============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置参数
NEO4J_PASSWORD="stageminder2024"
NEO4J_USERNAME="neo4j"
NEO4J_DATABASE="neo4j"
CONTAINER_NAME="stageminder-neo4j-shared"

print_header() {
    echo -e "${CYAN}"
    echo "=================================================="
    echo "🚀 StageMinder 本地一键部署脚本"
    echo "📅 时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "=================================================="
    echo -e "${NC}"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 检查Docker环境
check_docker() {
    print_step "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker Desktop"
        exit 1
    fi
    
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker未运行，请启动Docker Desktop"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose未安装"
        exit 1
    fi
    
    print_success "Docker环境检查通过"
}

# 停止现有服务
stop_existing_services() {
    print_step "停止现有服务..."
    
    # 停止所有可能的服务
    docker-compose down 2>/dev/null || true
    docker-compose -f docker-compose.main.yml down 2>/dev/null || true
    docker-compose -f docker-compose.neo4j.yml down 2>/dev/null || true
    
    # 停止子项目
    if [[ -d "StageMinder" ]]; then
        cd StageMinder
        docker-compose down 2>/dev/null || true
        docker-compose -f docker-compose-optimized.yml down 2>/dev/null || true
        cd ..
    fi
    
    if [[ -d "adminconsole" ]]; then
        cd adminconsole
        docker-compose down 2>/dev/null || true
        docker-compose -f docker-compose-optimized.yml down 2>/dev/null || true
        cd ..
    fi
    
    # 停止并删除Neo4j容器
    docker stop $(docker ps -q --filter "name=neo4j") 2>/dev/null || true
    docker rm $(docker ps -aq --filter "name=neo4j") 2>/dev/null || true
    
    print_success "现有服务已停止"
}

# 修复配置文件
fix_configurations() {
    print_step "修复配置文件..."
    
    # 修复应用配置文件
    find . -name "application*.properties" -type f | while read -r file; do
        if [[ -f "$file" ]]; then
            cp "$file" "$file.backup" 2>/dev/null || true
            sed -i "s/spring.neo4j.authentication.password=.*/spring.neo4j.authentication.password=$NEO4J_PASSWORD/g" "$file" 2>/dev/null || true
            sed -i "s/spring.neo4j.authentication.username=.*/spring.neo4j.authentication.username=$NEO4J_USERNAME/g" "$file" 2>/dev/null || true
        fi
    done
    
    # 修复YAML配置
    find . -name "application*.yml" -type f | while read -r file; do
        if [[ -f "$file" ]]; then
            cp "$file" "$file.backup" 2>/dev/null || true
            sed -i "s/password: .*/password: \${SPRING_NEO4J_AUTHENTICATION_PASSWORD:$NEO4J_PASSWORD}/g" "$file" 2>/dev/null || true
        fi
    done
    
    # 修复Docker Compose文件
    find . -name "docker-compose*.yml" -type f | while read -r file; do
        if [[ -f "$file" ]] && grep -q "neo4j" "$file"; then
            cp "$file" "$file.backup" 2>/dev/null || true
            sed -i "s/NEO4J_AUTH=.*/NEO4J_AUTH=$NEO4J_USERNAME\/$NEO4J_PASSWORD/g" "$file" 2>/dev/null || true
            sed -i "s/NEO4J_PASSWORD=.*/NEO4J_PASSWORD=$NEO4J_PASSWORD/g" "$file" 2>/dev/null || true
            sed -i "s/container_name: stageminder-neo4j$/container_name: $CONTAINER_NAME/g" "$file" 2>/dev/null || true
        fi
    done
    
    print_success "配置文件修复完成"
}

# 创建本地Docker Compose配置
create_local_compose() {
    print_step "创建本地部署配置..."
    
    cat > docker-compose.local.yml << 'EOF'
version: '3.8'

services:
  # Neo4j Database
  neo4j:
    image: neo4j:5.15-community
    container_name: stageminder-neo4j-shared
    environment:
      - NEO4J_AUTH=neo4j/stageminder2024
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=512m
      - NEO4J_dbms_connector_bolt_listen__address=0.0.0.0:7687
      - NEO4J_dbms_connector_http_listen__address=0.0.0.0:7474
      - NEO4J_dbms_default__database=neo4j
    ports:
      - "7474:7474"
      - "7687:7687"
    volumes:
      - stageminder_neo4j_data:/data
      - stageminder_neo4j_logs:/logs
      - stageminder_neo4j_conf:/conf
    networks:
      - stageminder-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "cypher-shell -u neo4j -p stageminder2024 'RETURN 1'"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Backend Service
  backend:
    build:
      context: ./StageMinder/Build/backend
      dockerfile: Dockerfile
    container_name: stageminder-backend
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=stageminder2024
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_REGION=us-east-1
      - S3_BUCKET_NAME=test-bucket
    ports:
      - "8080:8080"
    depends_on:
      neo4j:
        condition: service_healthy
    networks:
      - stageminder-network
    restart: unless-stopped

  # Frontend Service
  frontend:
    build:
      context: ./StageMinder/Build/frontend
      dockerfile: Dockerfile
    container_name: stageminder-frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8080
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - stageminder-network
    restart: unless-stopped

  # Nginx Proxy
  nginx:
    image: nginx:alpine
    container_name: stageminder-nginx
    ports:
      - "80:80"
    volumes:
      - ./StageMinder/Build/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - frontend
      - backend
    networks:
      - stageminder-network
    restart: unless-stopped

  # Admin Console Backend
  admin-console:
    build:
      context: ./adminconsole
      dockerfile: Dockerfile
    container_name: stageminder-admin-console
    environment:
      - SPRING_NEO4J_URI=bolt://neo4j:7687
      - SPRING_NEO4J_AUTHENTICATION_USERNAME=neo4j
      - SPRING_NEO4J_AUTHENTICATION_PASSWORD=stageminder2024
    ports:
      - "8081:8081"
    depends_on:
      neo4j:
        condition: service_healthy
    networks:
      - stageminder-network
    restart: unless-stopped

  # Admin Console Frontend
  admin-frontend:
    image: nginx:alpine
    container_name: admin-frontend
    ports:
      - "3001:80"
    volumes:
      - ./adminconsole:/usr/share/nginx/html:ro
    networks:
      - stageminder-network
    restart: unless-stopped

volumes:
  stageminder_neo4j_data:
  stageminder_neo4j_logs:
  stageminder_neo4j_conf:

networks:
  stageminder-network:
    driver: bridge
EOF
    
    print_success "本地部署配置创建完成"
}

# 恢复备份数据
restore_backup_data() {
    print_step "检查并恢复备份数据..."
    
    # 查找备份文件
    BACKUP_FILE=""
    if [[ -f neo4j-backup-*.zip ]]; then
        BACKUP_FILE=$(ls neo4j-backup-*.zip | head -1)
        print_info "发现备份文件: $BACKUP_FILE"
        
        # 解压备份
        unzip -q "$BACKUP_FILE" 2>/dev/null || print_warning "解压失败"
        BACKUP_DIR=$(echo "$BACKUP_FILE" | sed 's/.zip$//')
    elif [[ -d neo4j-backup-* ]]; then
        BACKUP_DIR=$(ls -d neo4j-backup-*/ | head -1)
        BACKUP_DIR="${BACKUP_DIR%/}"
        print_info "发现备份目录: $BACKUP_DIR"
    fi
    
    if [[ -n "$BACKUP_DIR" ]] && [[ -f "$BACKUP_DIR/neo4j-data-volume.tar.gz" ]]; then
        print_info "恢复备份数据..."
        
        # 创建数据卷
        docker volume create stageminder_neo4j_data 2>/dev/null || true
        
        # 恢复数据
        docker run --rm \
            -v stageminder_neo4j_data:/target \
            -v "$(pwd)/$BACKUP_DIR":/backup \
            alpine:latest \
            tar xzf /backup/neo4j-data-volume.tar.gz -C /target
        
        print_success "备份数据恢复完成"
    else
        print_info "未发现备份数据，将使用空数据库"
    fi
}

# 构建和启动服务
deploy_services() {
    print_step "构建和启动服务..."
    
    # 创建网络
    docker network create stageminder-network 2>/dev/null || true
    
    # 启动服务
    print_info "启动完整服务栈..."
    docker-compose -f docker-compose.local.yml up -d --build
    
    print_success "服务启动完成"
}

# 等待服务启动
wait_for_services() {
    print_step "等待服务启动..."
    
    print_info "等待Neo4j启动..."
    for i in {1..30}; do
        if docker exec stageminder-neo4j-shared cypher-shell -u neo4j -p stageminder2024 "RETURN 1" > /dev/null 2>&1; then
            print_success "Neo4j启动成功！"
            break
        fi
        if [[ $i -eq 30 ]]; then
            print_warning "Neo4j启动超时"
        fi
        echo -n "."
        sleep 2
    done
    
    print_info "等待后端服务启动..."
    for i in {1..20}; do
        if curl -f "http://localhost:8080/actuator/health" > /dev/null 2>&1; then
            print_success "后端服务启动成功！"
            break
        fi
        if [[ $i -eq 20 ]]; then
            print_warning "后端服务启动超时"
        fi
        echo -n "."
        sleep 3
    done
    
    print_success "服务启动检查完成"
}

# 验证部署
verify_deployment() {
    print_step "验证部署状态..."
    
    echo ""
    print_info "=== 服务状态 ==="
    docker-compose -f docker-compose.local.yml ps
    
    echo ""
    print_info "=== 健康检查 ==="
    
    # Neo4j检查
    if docker exec stageminder-neo4j-shared cypher-shell -u neo4j -p stageminder2024 "RETURN 1" > /dev/null 2>&1; then
        echo -e "  ✅ Neo4j: ${GREEN}正常${NC}"
    else
        echo -e "  ❌ Neo4j: ${RED}异常${NC}"
    fi
    
    # 后端检查
    if curl -f "http://localhost:8080/actuator/health" > /dev/null 2>&1; then
        echo -e "  ✅ 后端API: ${GREEN}正常${NC}"
    else
        echo -e "  ❌ 后端API: ${RED}异常${NC}"
    fi
    
    # 前端检查
    if curl -f "http://localhost:3000" > /dev/null 2>&1; then
        echo -e "  ✅ 前端: ${GREEN}正常${NC}"
    else
        echo -e "  ❌ 前端: ${RED}异常${NC}"
    fi
}

# 显示访问信息
show_access_info() {
    print_step "部署完成！"
    
    echo ""
    echo -e "${GREEN}🎉 StageMinder 本地部署成功！${NC}"
    echo ""
    echo -e "${CYAN}📍 访问地址:${NC}"
    echo "  🌐 主应用:        http://localhost"
    echo "  📊 Neo4j Browser: http://localhost:7474"
    echo "  🔧 管理控制台:    http://localhost:3001"
    echo "  📱 前端直接访问:  http://localhost:3000"
    echo "  🔌 后端API:       http://localhost:8080"
    echo "  ⚙️  Admin API:     http://localhost:8081"
    echo ""
    echo -e "${CYAN}🔐 登录信息:${NC}"
    echo "  Neo4j用户名: neo4j"
    echo "  Neo4j密码:   stageminder2024"
    echo ""
    echo -e "${CYAN}📋 管理命令:${NC}"
    echo "  查看状态:    docker-compose -f docker-compose.local.yml ps"
    echo "  查看日志:    docker-compose -f docker-compose.local.yml logs"
    echo "  重启服务:    docker-compose -f docker-compose.local.yml restart"
    echo "  停止服务:    docker-compose -f docker-compose.local.yml down"
    echo ""
}

# 主函数
main() {
    print_header
    
    check_docker
    stop_existing_services
    fix_configurations
    create_local_compose
    restore_backup_data
    deploy_services
    wait_for_services
    verify_deployment
    show_access_info
    
    print_success "本地部署脚本执行完成！"
}

# 错误处理
handle_error() {
    print_error "部署过程中发生错误！"
    echo ""
    print_info "故障排除建议："
    echo "  1. 检查Docker服务状态"
    echo "  2. 查看容器日志: docker-compose -f docker-compose.local.yml logs"
    echo "  3. 重新运行脚本: ./deploy-local.sh"
    exit 1
}

# 设置错误处理
trap handle_error ERR

# 运行主函数
main "$@"
