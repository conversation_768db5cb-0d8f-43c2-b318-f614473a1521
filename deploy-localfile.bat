@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM =============================================================================
REM Deploy Local Files Script for StageMinder Project
REM 
REM This script copies all necessary files to the deploy folder while
REM excluding build artifacts, IDE files, and other unnecessary files.
REM
REM Usage: deploy-localfile.bat
REM =============================================================================

REM Colors for Windows console
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "NC=[0m"

echo %CYAN%==================================================
echo 📁 StageMinder Local File Deployment Script
echo 📅 Time: %date% %time%
echo ==================================================%NC%
echo.

REM Set deploy directory
set "DEPLOY_DIR=deploy"

REM Step 1: Clean deploy directory
echo %BLUE%[STEP 1/2] Cleaning deploy directory...%NC%
if exist "%DEPLOY_DIR%" (
    echo %YELLOW%[INFO] Removing existing deploy directory contents...%NC%
    rmdir /s /q "%DEPLOY_DIR%" 2>nul
    if errorlevel 1 (
        echo %RED%[ERROR] Failed to clean deploy directory%NC%
        echo Some files might be in use. Please close all applications and try again.
        pause
        exit /b 1
    )
)

REM Create deploy directory
echo %BLUE%[INFO] Creating fresh deploy directory...%NC%
mkdir "%DEPLOY_DIR%" 2>nul

REM Step 2: Copy only essential source code and deployment files
echo %BLUE%[STEP 2/2] Copying essential source code and deployment files...%NC%
echo %YELLOW%[INFO] Only copying source code, configuration, and deployment files (no build outputs)...%NC%

REM Define directories to exclude (build outputs, dependencies, IDE files, etc.)
echo .git > "%TEMP%\exclude_dirs.txt"
echo node_modules >> "%TEMP%\exclude_dirs.txt"
echo target >> "%TEMP%\exclude_dirs.txt"
echo .next >> "%TEMP%\exclude_dirs.txt"
echo out >> "%TEMP%\exclude_dirs.txt"
echo dist >> "%TEMP%\exclude_dirs.txt"
echo build >> "%TEMP%\exclude_dirs.txt"
echo logs >> "%TEMP%\exclude_dirs.txt"
echo .idea >> "%TEMP%\exclude_dirs.txt"
echo .vscode >> "%TEMP%\exclude_dirs.txt"
echo .settings >> "%TEMP%\exclude_dirs.txt"
echo coverage >> "%TEMP%\exclude_dirs.txt"
echo deploy >> "%TEMP%\exclude_dirs.txt"
echo classes >> "%TEMP%\exclude_dirs.txt"
echo generated-sources >> "%TEMP%\exclude_dirs.txt"
echo generated-test-sources >> "%TEMP%\exclude_dirs.txt"
echo maven-status >> "%TEMP%\exclude_dirs.txt"
echo test-classes >> "%TEMP%\exclude_dirs.txt"
echo .mvn >> "%TEMP%\exclude_dirs.txt"
echo .turbo >> "%TEMP%\exclude_dirs.txt"
echo .vercel >> "%TEMP%\exclude_dirs.txt"
echo .nyc_output >> "%TEMP%\exclude_dirs.txt"

REM Define files to exclude (build outputs, logs, IDE files, temp files, etc.)
echo *.log > "%TEMP%\exclude_files.txt"
echo *.tmp >> "%TEMP%\exclude_files.txt"
echo *.temp >> "%TEMP%\exclude_files.txt"
echo *.cache >> "%TEMP%\exclude_files.txt"
echo .DS_Store >> "%TEMP%\exclude_files.txt"
echo Thumbs.db >> "%TEMP%\exclude_files.txt"
echo *.jar >> "%TEMP%\exclude_files.txt"
echo *.war >> "%TEMP%\exclude_files.txt"
echo *.ear >> "%TEMP%\exclude_files.txt"
echo *.class >> "%TEMP%\exclude_files.txt"
echo *.pid >> "%TEMP%\exclude_files.txt"
echo *.seed >> "%TEMP%\exclude_files.txt"
echo *.pid.lock >> "%TEMP%\exclude_files.txt"
echo *.backup >> "%TEMP%\exclude_files.txt"
echo *.orig >> "%TEMP%\exclude_files.txt"
echo .env.local >> "%TEMP%\exclude_files.txt"
echo .env.development.local >> "%TEMP%\exclude_files.txt"
echo .env.test.local >> "%TEMP%\exclude_files.txt"
echo .env.production.local >> "%TEMP%\exclude_files.txt"
echo npm-debug.log* >> "%TEMP%\exclude_files.txt"
echo yarn-debug.log* >> "%TEMP%\exclude_files.txt"
echo yarn-error.log* >> "%TEMP%\exclude_files.txt"
echo pnpm-debug.log* >> "%TEMP%\exclude_files.txt"
echo *.tsbuildinfo >> "%TEMP%\exclude_files.txt"
echo *.swp >> "%TEMP%\exclude_files.txt"
echo *.swo >> "%TEMP%\exclude_files.txt"
echo *.iml >> "%TEMP%\exclude_files.txt"
echo *.ipr >> "%TEMP%\exclude_files.txt"
echo *.iws >> "%TEMP%\exclude_files.txt"
echo .project >> "%TEMP%\exclude_files.txt"
echo .classpath >> "%TEMP%\exclude_files.txt"
echo *.exe >> "%TEMP%\exclude_files.txt"
echo *.dll >> "%TEMP%\exclude_files.txt"
echo admin-console-*.jar >> "%TEMP%\exclude_files.txt"
echo admin-console-*.jar.original >> "%TEMP%\exclude_files.txt"
echo pom.properties >> "%TEMP%\exclude_files.txt"
echo HELP.md >> "%TEMP%\exclude_files.txt"

REM First copy only essential deployment files using selective approach
echo %BLUE%[INFO] Copying essential deployment files only...%NC%

REM Copy root level deployment files
for %%f in (*.sh *.bat *.yml *.yaml *.md) do (
    if exist "%%f" copy "%%f" "%DEPLOY_DIR%\" >nul 2>&1
)

REM Copy adminconsole source files (excluding target directory)
echo %BLUE%[INFO] Copying adminconsole source files...%NC%
if exist "adminconsole\" (
    xcopy "adminconsole\src" "%DEPLOY_DIR%\adminconsole\src\" /E /I /Q >nul 2>&1
    xcopy "adminconsole\pom.xml" "%DEPLOY_DIR%\adminconsole\" /Y /Q >nul 2>&1
    xcopy "adminconsole\Dockerfile*" "%DEPLOY_DIR%\adminconsole\" /Y /Q >nul 2>&1
    xcopy "adminconsole\docker-compose*.yml" "%DEPLOY_DIR%\adminconsole\" /Y /Q >nul 2>&1
    xcopy "adminconsole\*.html" "%DEPLOY_DIR%\adminconsole\" /Y /Q >nul 2>&1
    xcopy "adminconsole\*.css" "%DEPLOY_DIR%\adminconsole\" /Y /Q >nul 2>&1
    xcopy "adminconsole\*.js" "%DEPLOY_DIR%\adminconsole\" /Y /Q >nul 2>&1
    xcopy "adminconsole\*.webp" "%DEPLOY_DIR%\adminconsole\" /Y /Q >nul 2>&1
    xcopy "adminconsole\*.png" "%DEPLOY_DIR%\adminconsole\" /Y /Q >nul 2>&1
    xcopy "adminconsole\README.md" "%DEPLOY_DIR%\adminconsole\" /Y /Q >nul 2>&1
    xcopy "adminconsole\*.md" "%DEPLOY_DIR%\adminconsole\" /Y /Q >nul 2>&1
)

REM Copy StageMinder source files and configuration
echo %BLUE%[INFO] Copying StageMinder source files...%NC%
if exist "StageMinder\" (
    REM Copy backend source files (excluding target, classes, generated-sources, etc.)
    xcopy "StageMinder\Build\backend\src" "%DEPLOY_DIR%\StageMinder\Build\backend\src\" /E /I /Q >nul 2>&1
    xcopy "StageMinder\Build\backend\pom.xml" "%DEPLOY_DIR%\StageMinder\Build\backend\" /Y /Q >nul 2>&1
    xcopy "StageMinder\Build\backend\Dockerfile*" "%DEPLOY_DIR%\StageMinder\Build\backend\" /Y /Q >nul 2>&1
    xcopy "StageMinder\Build\backend\mvnw*" "%DEPLOY_DIR%\StageMinder\Build\backend\" /Y /Q >nul 2>&1
    xcopy "StageMinder\Build\backend\*.sh" "%DEPLOY_DIR%\StageMinder\Build\backend\" /Y /Q >nul 2>&1
    xcopy "StageMinder\Build\backend\*.cypher" "%DEPLOY_DIR%\StageMinder\Build\backend\" /Y /Q >nul 2>&1
    xcopy "StageMinder\Build\backend\*.json" "%DEPLOY_DIR%\StageMinder\Build\backend\" /Y /Q >nul 2>&1
    xcopy "StageMinder\Build\backend\json-files" "%DEPLOY_DIR%\StageMinder\Build\backend\json-files\" /E /I /Q >nul 2>&1
    xcopy "StageMinder\Build\backend\images" "%DEPLOY_DIR%\StageMinder\Build\backend\images\" /E /I /Q >nul 2>&1
    xcopy "StageMinder\Build\backend\pdf-files" "%DEPLOY_DIR%\StageMinder\Build\backend\pdf-files\" /E /I /Q >nul 2>&1
    xcopy "StageMinder\Build\backend\scripts" "%DEPLOY_DIR%\StageMinder\Build\backend\scripts\" /E /I /Q >nul 2>&1
    xcopy "StageMinder\Build\backend\README.md" "%DEPLOY_DIR%\StageMinder\Build\backend\" /Y /Q >nul 2>&1
    
    REM Copy frontend source files (excluding node_modules, .next, dist, etc.)
    xcopy "StageMinder\Build\frontend\src" "%DEPLOY_DIR%\StageMinder\Build\frontend\src\" /E /I /Q >nul 2>&1
    xcopy "StageMinder\Build\frontend\public" "%DEPLOY_DIR%\StageMinder\Build\frontend\public\" /E /I /Q >nul 2>&1
    xcopy "StageMinder\Build\frontend\messages" "%DEPLOY_DIR%\StageMinder\Build\frontend\messages\" /E /I /Q >nul 2>&1
    xcopy "StageMinder\Build\frontend\scripts" "%DEPLOY_DIR%\StageMinder\Build\frontend\scripts\" /E /I /Q >nul 2>&1
    xcopy "StageMinder\Build\frontend\package*.json" "%DEPLOY_DIR%\StageMinder\Build\frontend\" /Y /Q >nul 2>&1
    xcopy "StageMinder\Build\frontend\Dockerfile*" "%DEPLOY_DIR%\StageMinder\Build\frontend\" /Y /Q >nul 2>&1
    xcopy "StageMinder\Build\frontend\*.config.js" "%DEPLOY_DIR%\StageMinder\Build\frontend\" /Y /Q >nul 2>&1
    xcopy "StageMinder\Build\frontend\*.config.mjs" "%DEPLOY_DIR%\StageMinder\Build\frontend\" /Y /Q >nul 2>&1
    xcopy "StageMinder\Build\frontend\jsconfig.json" "%DEPLOY_DIR%\StageMinder\Build\frontend\" /Y /Q >nul 2>&1
    xcopy "StageMinder\Build\frontend\dot.env" "%DEPLOY_DIR%\StageMinder\Build\frontend\" /Y /Q >nul 2>&1
    xcopy "StageMinder\Build\frontend\README.md" "%DEPLOY_DIR%\StageMinder\Build\frontend\" /Y /Q >nul 2>&1
    
    REM Copy other StageMinder files
    xcopy "StageMinder\*.sh" "%DEPLOY_DIR%\StageMinder\" /Y /Q >nul 2>&1
    xcopy "StageMinder\*.yml" "%DEPLOY_DIR%\StageMinder\" /Y /Q >nul 2>&1
    xcopy "StageMinder\docker-compose*.yml" "%DEPLOY_DIR%\StageMinder\" /Y /Q >nul 2>&1
    xcopy "StageMinder\README.md" "%DEPLOY_DIR%\StageMinder\" /Y /Q >nul 2>&1
    xcopy "StageMinder\images" "%DEPLOY_DIR%\StageMinder\images\" /E /I /Q >nul 2>&1
    xcopy "StageMinder\Scripts" "%DEPLOY_DIR%\StageMinder\Scripts\" /E /I /Q >nul 2>&1
    xcopy "StageMinder\Run" "%DEPLOY_DIR%\StageMinder\Run\" /E /I /Q >nul 2>&1
    xcopy "StageMinder\*.pdf" "%DEPLOY_DIR%\StageMinder\" /Y /Q >nul 2>&1
    xcopy "StageMinder\Build\README.md" "%DEPLOY_DIR%\StageMinder\Build\" /Y /Q >nul 2>&1
)

REM Clean up temporary files
echo %BLUE%[INFO] Cleaning up temporary files...%NC%
del "%TEMP%\exclude_dirs.txt" 2>nul
del "%TEMP%\exclude_files.txt" 2>nul

REM Display results
echo.
echo %GREEN%[SUCCESS] Files successfully copied to deploy directory!%NC%
echo.

REM Show deploy directory contents summary
echo %CYAN%=== Deploy Directory Summary ===%NC%
for /f %%i in ('dir "%DEPLOY_DIR%" /s /a-d /q ^| find /c ":"') do set file_count=%%i
echo %BLUE%[INFO] Total files copied: %file_count%%NC%

echo.
echo %CYAN%=== Top-level contents in deploy directory ===%NC%
dir "%DEPLOY_DIR%" /b
echo.

echo %CYAN%=== What was EXCLUDED (build outputs and unnecessary files) ===%NC%
echo %YELLOW%Directories:%NC% .git, node_modules, target, .next, out, dist, build, logs, .idea, .vscode, .settings, coverage, classes, generated-sources, test-classes, .mvn, .turbo, .vercel
echo %YELLOW%File types:%NC% *.log, *.tmp, *.jar, *.war, *.class, *.pid, .env.local, npm-debug.log*, *.backup, *.orig, *.exe, *.dll, IDE files, OS files
echo.
echo %CYAN%=== What was INCLUDED (source code and deployment files only) ===%NC%
echo %GREEN%✓ Source code files:%NC% *.java, *.js, *.jsx, *.ts, *.tsx, *.css, *.html
echo %GREEN%✓ Configuration files:%NC% pom.xml, package.json, *.yml, *.json, *.env, *.conf
echo %GREEN%✓ Deployment files:%NC% Dockerfile*, docker-compose*.yml, *.sh, *.bat
echo %GREEN%✓ Static assets:%NC% *.png, *.svg, *.webp, *.pdf (source files only)
echo %GREEN%✓ Documentation:%NC% README.md, *.md
echo.

echo %GREEN%✅ Deploy preparation completed successfully!%NC%
echo %BLUE%[INFO] You can now use the contents of the '%DEPLOY_DIR%' directory for deployment.%NC%
echo.

goto :end

:cleanup_and_exit
echo %RED%[ERROR] Deploy preparation failed!%NC%
del "%TEMP%\exclude_dirs.txt" 2>nul
del "%TEMP%\exclude_files.txt" 2>nul
echo.
pause
exit /b 1

:end
echo %BLUE%[INFO] Script execution completed at %time%%NC%
echo.
pause
